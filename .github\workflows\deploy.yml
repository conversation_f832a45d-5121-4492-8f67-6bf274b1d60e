name: Deploy Flask App to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flask jinja2 markupsafe
        
    - name: Create build directory
      run: |
        mkdir -p dist
        mkdir -p dist/static
        mkdir -p dist/templates
        
    - name: Generate static HTML from Flask templates
      run: |
        python -c "
        import os
        from flask import Flask, render_template_string
        
        app = Flask(__name__)
        
        # Create index.html from template
        index_template = '''
        <!DOCTYPE html>
        <html lang=\"en\">
        <head>
            <meta charset=\"UTF-8\">
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
            <title>Fabric Defect Detection - AI-Powered Quality Control</title>
            <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
            <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">
            <style>
                .hero-section {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 100px 0;
                }
                .feature-card {
                    transition: transform 0.3s;
                    height: 100%;
                }
                .feature-card:hover {
                    transform: translateY(-5px);
                }
                .demo-section {
                    background-color: #f8f9fa;
                    padding: 80px 0;
                }
                .tech-badge {
                    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                    color: white;
                    padding: 5px 15px;
                    border-radius: 20px;
                    margin: 5px;
                    display: inline-block;
                }
                .upload-area {
                    border: 2px dashed #ccc;
                    border-radius: 10px;
                    padding: 40px;
                    text-align: center;
                    background-color: #f9f9f9;
                    cursor: pointer;
                    transition: all 0.3s;
                }
                .upload-area:hover {
                    border-color: #007bff;
                    background-color: #f0f8ff;
                }
            </style>
        </head>
        <body>
            <!-- Navigation -->
            <nav class=\"navbar navbar-expand-lg navbar-dark bg-dark fixed-top\">
                <div class=\"container\">
                    <a class=\"navbar-brand\" href=\"#\"><i class=\"fas fa-search\"></i> Fabric Defect Detection</a>
                    <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\">
                        <span class=\"navbar-toggler-icon\"></span>
                    </button>
                    <div class=\"collapse navbar-collapse\" id=\"navbarNav\">
                        <ul class=\"navbar-nav ms-auto\">
                            <li class=\"nav-item\"><a class=\"nav-link\" href=\"#features\">Features</a></li>
                            <li class=\"nav-item\"><a class=\"nav-link\" href=\"#demo\">Demo</a></li>
                            <li class=\"nav-item\"><a class=\"nav-link\" href=\"#deploy\">Deploy</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Hero Section -->
            <section class=\"hero-section\">
                <div class=\"container text-center\">
                    <h1 class=\"display-4 fw-bold mb-4\">AI-Powered Fabric Defect Detection</h1>
                    <p class=\"lead mb-5\">Advanced computer vision technology using YOLOv8 to detect and classify fabric defects in real-time</p>
                    <div class=\"alert alert-warning mx-auto\" style=\"max-width: 600px;\">
                        <i class=\"fas fa-info-circle\"></i> <strong>Note:</strong> This is a static demo. For the full Flask application with AI detection, deploy to Heroku using the button below.
                    </div>
                    <div class=\"d-grid gap-2 d-md-flex justify-content-md-center\">
                        <a href=\"https://heroku.com/deploy?template=https://github.com/LOVEPOISON11/fabric-defect-detection\" class=\"btn btn-light btn-lg me-md-2\" target=\"_blank\">
                            <i class=\"fas fa-rocket\"></i> Deploy Full App
                        </a>
                        <a href=\"https://github.com/LOVEPOISON11/fabric-defect-detection\" class=\"btn btn-outline-light btn-lg\" target=\"_blank\">
                            <i class=\"fab fa-github\"></i> View Code
                        </a>
                    </div>
                </div>
            </section>

            <!-- Demo Section -->
            <section id=\"demo\" class=\"demo-section\">
                <div class=\"container\">
                    <div class=\"row text-center mb-5\">
                        <div class=\"col\">
                            <h2 class=\"display-5 fw-bold\">Static Demo Interface</h2>
                            <p class=\"lead text-muted\">This shows how the interface looks (AI detection requires full deployment)</p>
                        </div>
                    </div>
                    <div class=\"row justify-content-center\">
                        <div class=\"col-md-8\">
                            <div class=\"card shadow-lg\">
                                <div class=\"card-body p-4\">
                                    <div class=\"upload-area\" onclick=\"alert('This is a static demo. Deploy the full app to Heroku for actual AI detection!')\">
                                        <i class=\"fas fa-cloud-upload-alt fa-3x text-muted mb-3\"></i>
                                        <h5>Click to Upload Fabric Image</h5>
                                        <p class=\"text-muted\">Drag and drop or click to select fabric images for defect detection</p>
                                        <small class=\"text-warning\">⚠️ Static demo - Deploy to Heroku for actual functionality</small>
                                    </div>
                                    <div class=\"mt-3\">
                                        <label for=\"confidence\" class=\"form-label\">Confidence Threshold: <span id=\"confidenceValue\">0.4</span></label>
                                        <input type=\"range\" class=\"form-range\" id=\"confidence\" min=\"0.1\" max=\"1\" step=\"0.1\" value=\"0.4\" onchange=\"document.getElementById('confidenceValue').textContent = this.value\">
                                    </div>
                                    <button class=\"btn btn-primary w-100 mt-3\" onclick=\"alert('Deploy the full Flask app to Heroku for AI-powered defect detection!')\">
                                        <i class=\"fas fa-search\"></i> Detect Defects (Deploy to Enable)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section id=\"features\" class=\"py-5\">
                <div class=\"container\">
                    <div class=\"row text-center mb-5\">
                        <div class=\"col\">
                            <h2 class=\"display-5 fw-bold\">Key Features</h2>
                            <p class=\"lead text-muted\">Comprehensive fabric quality control solution</p>
                        </div>
                    </div>
                    <div class=\"row g-4\">
                        <div class=\"col-md-4\">
                            <div class=\"card feature-card shadow-sm\">
                                <div class=\"card-body text-center p-4\">
                                    <i class=\"fas fa-upload fa-3x text-primary mb-3\"></i>
                                    <h5 class=\"card-title\">Image Upload Detection</h5>
                                    <p class=\"card-text\">Upload fabric images and get instant defect detection results with bounding boxes and confidence scores.</p>
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"card feature-card shadow-sm\">
                                <div class=\"card-body text-center p-4\">
                                    <i class=\"fas fa-video fa-3x text-success mb-3\"></i>
                                    <h5 class=\"card-title\">Real-time Camera Detection</h5>
                                    <p class=\"card-text\">Use your webcam for live fabric defect monitoring with real-time FPS and defect statistics.</p>
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"card feature-card shadow-sm\">
                                <div class=\"card-body text-center p-4\">
                                    <i class=\"fas fa-cog fa-3x text-warning mb-3\"></i>
                                    <h5 class=\"card-title\">Adjustable Confidence</h5>
                                    <p class=\"card-text\">Fine-tune detection sensitivity with adjustable confidence thresholds for optimal results.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Deployment Section -->
            <section id=\"deploy\" class=\"py-5 bg-light\">
                <div class=\"container\">
                    <div class=\"row text-center mb-5\">
                        <div class=\"col\">
                            <h2 class=\"display-5 fw-bold\">Deploy Full Application</h2>
                            <p class=\"lead text-muted\">Get your fabric defect detection system running with AI</p>
                        </div>
                    </div>
                    <div class=\"row justify-content-center\">
                        <div class=\"col-md-8\">
                            <div class=\"card shadow-lg\">
                                <div class=\"card-body p-5 text-center\">
                                    <i class=\"fas fa-rocket fa-4x text-primary mb-4\"></i>
                                    <h4 class=\"mb-3\">Deploy to Heroku</h4>
                                    <p class=\"mb-4\">The full Flask application with YOLOv8 AI model for actual fabric defect detection.</p>
                                    <div class=\"d-grid gap-2 d-md-flex justify-content-md-center\">
                                        <a href=\"https://heroku.com/deploy?template=https://github.com/LOVEPOISON11/fabric-defect-detection\" 
                                           class=\"btn btn-primary btn-lg me-md-2\" target=\"_blank\">
                                            <i class=\"fas fa-cloud\"></i> Deploy to Heroku
                                        </a>
                                        <a href=\"https://github.com/LOVEPOISON11/fabric-defect-detection\" 
                                           class=\"btn btn-outline-primary btn-lg\" target=\"_blank\">
                                            <i class=\"fab fa-github\"></i> View Source
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Footer -->
            <footer class=\"bg-dark text-white py-4\">
                <div class=\"container text-center\">
                    <p class=\"mb-2\">&copy; 2024 Fabric Defect Detection. Built with ❤️ for the textile industry.</p>
                    <p class=\"mb-0\">
                        <a href=\"https://github.com/LOVEPOISON11/fabric-defect-detection\" class=\"text-white me-3\" target=\"_blank\">
                            <i class=\"fab fa-github\"></i> GitHub
                        </a>
                        <a href=\"mailto:<EMAIL>\" class=\"text-white\">
                            <i class=\"fas fa-envelope\"></i> Contact
                        </a>
                    </p>
                </div>
            </footer>

            <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js\"></script>
        </body>
        </html>
        '''
        
        with open('dist/index.html', 'w', encoding='utf-8') as f:
            f.write(index_template)
        
        print('✅ Static HTML generated successfully')
        "
        
    - name: Copy additional files
      run: |
        # Copy README for documentation
        cp README.md dist/ 2>/dev/null || echo "No README found"
        
        # Create a simple 404 page
        echo '<!DOCTYPE html>
        <html><head><title>Page Not Found</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
        <h1>404 - Page Not Found</h1>
        <p><a href="/">Return to Fabric Defect Detection</a></p>
        </body></html>' > dist/404.html
        
        # Create robots.txt
        echo 'User-agent: *
        Allow: /' > dist/robots.txt
        
    - name: Setup Pages
      uses: actions/configure-pages@v4
      
    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: ./dist

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
